"""Response schemas for API endpoints."""

from typing import Any, Dict, Generic, List, Optional, TypeVar

from pydantic import BaseModel, Field

from .base import BaseSchema

DataType = TypeVar("DataType")


class ResponseModel(BaseSchema, Generic[DataType]):
    """Generic response model."""
    
    success: bool = Field(description="Whether the request was successful")
    message: str = Field(description="Response message")
    data: Optional[DataType] = Field(default=None, description="Response data")
    errors: Optional[List[str]] = Field(default=None, description="List of errors")


class ErrorResponse(BaseSchema):
    """Error response model."""
    
    success: bool = Field(default=False, description="Always false for errors")
    message: str = Field(description="Error message")
    errors: List[str] = Field(description="List of detailed errors")
    error_code: Optional[str] = Field(default=None, description="Error code")


class SuccessResponse(BaseSchema, Generic[DataType]):
    """Success response model."""
    
    success: bool = Field(default=True, description="Always true for success")
    message: str = Field(description="Success message")
    data: DataType = Field(description="Response data")


class PaginatedResponse(BaseSchema, Generic[DataType]):
    """Paginated response model."""
    
    success: bool = Field(default=True, description="Whether the request was successful")
    message: str = Field(description="Response message")
    data: List[DataType] = Field(description="List of items")
    pagination: Dict[str, Any] = Field(description="Pagination metadata")
    
    @classmethod
    def create(
        cls,
        items: List[DataType],
        total: int,
        page: int,
        size: int,
        message: str = "Success"
    ) -> "PaginatedResponse[DataType]":
        """Create a paginated response."""
        total_pages = (total + size - 1) // size  # Ceiling division
        
        return cls(
            message=message,
            data=items,
            pagination={
                "total": total,
                "page": page,
                "size": size,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1,
            }
        )
